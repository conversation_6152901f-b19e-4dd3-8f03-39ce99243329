{"name": "sample-test-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3005", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "lucide-react": "^0.503.0", "next": "15.3.1", "papaparse": "^5.5.2", "pino": "^9.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tslib": "^2.8.1", "tw-animate-css": "^1.2.8", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/csv-parse": "^1.2.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}