{"scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "dotenv": "^16.4.5", "openai": "^4.87.3", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "playwright": "^1.52.0", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20", "ts-node-dev": "^2.0.0", "typescript": "^5"}}