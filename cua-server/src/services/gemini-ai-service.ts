import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import logger from "../utils/logger";
import { TestCase } from "../agents/test-case-agent";

/**
 * Gemini AI Service - Free alternative to OpenAI API
 * Uses Google's Gemini API with generous free tier
 */

class GeminiAIService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("GEMINI_API_KEY environment variable is required");
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    // Use gemini-1.5-flash for free tier (generous limits)
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    logger.info("🤖 Gemini AI Service initialized (FREE TIER)");
  }

  /**
   * Generate test case using Gemini
   */
  async generateTestCase(userInstruction: string, loginRequired: boolean = false): Promise<TestCase> {
    logger.info("🆓 Using FREE Gemini API for test case generation");
    
    const systemPrompt = loginRequired ? this.getLoginPrompt() : this.getNoLoginPrompt();
    const fullPrompt = `${systemPrompt}\n\nUser Request: ${userInstruction}\n\nRespond with valid JSON only:`;
    
    try {
      const result = await this.model.generateContent(fullPrompt);
      const response = await result.response;
      const text = response.text();
      
      logger.debug("Raw Gemini response:", text);
      
      // Extract JSON from response (Gemini sometimes adds extra text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No valid JSON found in Gemini response");
      }
      
      const testCase = JSON.parse(jsonMatch[0]);
      logger.debug("Parsed test case:", { steps: testCase.steps?.length });
      
      return testCase;
    } catch (error) {
      logger.error("Error generating test case with Gemini:", error);
      // Fallback to a basic test case
      return this.getFallbackTestCase(loginRequired);
    }
  }

  /**
   * Review test script using Gemini with image analysis
   */
  async reviewTestScript(instruction: string, base64Image?: string): Promise<string> {
    logger.info("🆓 Using FREE Gemini API for test script review");
    
    const prompt = `${this.getReviewPrompt()}\n\nInstructions: ${instruction}\n\nAnalyze the screenshot and update the test status. Respond with valid JSON only:`;
    
    try {
      let result;
      
      if (base64Image) {
        // Gemini can analyze images
        result = await this.model.generateContent([
          prompt,
          {
            inlineData: {
              data: base64Image,
              mimeType: "image/png"
            }
          }
        ]);
      } else {
        result = await this.model.generateContent(prompt);
      }
      
      const response = await result.response;
      const text = response.text();
      
      logger.debug("Raw Gemini review response:", text);
      
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No valid JSON found in Gemini response");
      }
      
      return jsonMatch[0];
    } catch (error) {
      logger.error("Error reviewing test script with Gemini:", error);
      // Return a basic review response
      return JSON.stringify({
        steps: [
          {
            step_number: 1,
            status: "pending",
            step_reasoning: "Test execution in progress"
          }
        ]
      });
    }
  }

  private getLoginPrompt(): string {
    return `You are a test case authoring agent. Create test steps in JSON format.
The first 3 steps are always:
1. Open the browser and navigate to the login URL.
2. Enter username and password.
3. Click Log In and verify successful sign-in.

Then add the actual test steps the user asked for.

Return JSON in this exact format:
{
  "steps": [
    { "step_number": 1, "step_instructions": "Open the browser and navigate to the login URL", "status": null },
    { "step_number": 2, "step_instructions": "Enter username and password", "status": null },
    { "step_number": 3, "step_instructions": "Click Log In and verify successful sign-in", "status": null }
  ]
}`;
  }

  private getNoLoginPrompt(): string {
    return `You are a test case authoring agent. Create test steps in JSON format.
The first step is always:
1. Open the browser and navigate to the target URL.

Then add the actual test steps the user asked for. Do not include login steps.

Return JSON in this exact format:
{
  "steps": [
    { "step_number": 1, "step_instructions": "Open a web browser and navigate to the target URL", "status": null }
  ]
}`;
  }

  private getReviewPrompt(): string {
    return `You are a test script review agent. Analyze the screenshot and update test step status.

Reply with JSON in this exact format:
{
  "steps": [
    {
      "step_number": 1,
      "status": "Pass" | "Fail" | "pending",
      "step_reasoning": "explanation of what you see"
    }
  ]
}

Do not add or remove steps. Only update status and reasoning.`;
  }

  private getFallbackTestCase(loginRequired: boolean): TestCase {
    const baseSteps = loginRequired ? [
      { step_number: 1, step_instructions: "Open the browser and navigate to the login URL", status: null },
      { step_number: 2, step_instructions: "Enter username and password", status: null },
      { step_number: 3, step_instructions: "Click Log In and verify successful sign-in", status: null },
      { step_number: 4, step_instructions: "Navigate to the target section", status: null }
    ] : [
      { step_number: 1, step_instructions: "Open a web browser and navigate to the target URL", status: null },
      { step_number: 2, step_instructions: "Navigate to the target section", status: null }
    ];

    return { steps: baseSteps };
  }
}

export const geminiAIService = new GeminiAIService();
